base on schema (inquiry.prisma), i want to implementation assign function.
- assign to a user.
- after assign, send notice to them.

- what should i need to do. let analyze project and create plan, update plan to this file -> augment-code/INQUIRY-ASSIGNMENT.md


i want you reference this file augment-code/INQUIRY-COMMENT-PLAN.md, and write more detail about task, update to // File 1: /augment-code/INQUIRY-ASSIGNMENT.md
