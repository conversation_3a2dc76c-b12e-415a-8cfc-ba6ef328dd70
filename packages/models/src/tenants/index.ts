import { subDays } from 'date-fns';
import type { Prisma } from '../../../../lambdas/.prisma';

import { edogawa } from './edogawa';
import { oec } from './oec';
import { shinagawa } from './shinagawa';
import type { TenantInput } from './tenant';

export const tenants: TenantInput[] = [oec, shinagawa, edogawa];

export const userFirstNames = [
  '太郎',
  '一郎',
  '二郎',
  '三郎',
  '四郎',
  '五郎',
  '六郎',
  '七郎',
  '八郎',
  '九郎',
];

export const notifications = [
  {
    title: 'システムアップデート完了',
    description: 'システムのアップデートが完了しました',
    type: 'system',
    scope: 'all',
    createdAt: subDays(new Date(), 10),
  },
  {
    title: 'チーム変更のお知らせ',
    description:
      '10月1日よりチームの編成を変更しているため、各自マイページよりご確認をお願いします。',
    type: 'user',
    scope: 'all',
    createdAt: subDays(new Date(), 2),
  },
] as const satisfies Prisma.NotificationUncheckedCreateInput[];
