import React from 'react';

import { Delete, Download, InsertDriveFile, MoreVert, OpenInNew } from '@mui/icons-material';
import {
  Box,
  Fab,
  IconButton,
  ImageListItemBar,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Skeleton,
  type SxProps,
} from '@mui/material';

import { downloadFromUrl } from 'common';

import useContentType from '@/hooks/useContentType';
import { BASE_URL } from '@/types/vite-env';

import { enableUrl } from 'models';
import { ImagePreviewDialog } from './ImagePreviewDialog';

type NativeImageProps = React.DetailedHTMLProps<
  React.ImgHTMLAttributes<HTMLImageElement>,
  HTMLImageElement
>;
type Props = Required<Pick<NativeImageProps, 'src' | 'alt'>> &
  NativeImageProps & {
    remove?: boolean;
    bar?: boolean;
    showPreviewButton?: boolean;
    onRemove?: () => void;
  };

export default function FileViewer({
  src,
  alt,
  remove,
  bar,
  showPreviewButton,
  onRemove,
  ...props
}: Props) {
  const { data: contentType, isLoading } = useContentType(src);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [showPreview, setShowPreview] = React.useState(false);

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleClickMore = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClickDownload = () => {
    setAnchorEl(null);
    downloadFromUrl(BASE_URL, src);
  };

  const open = Boolean(anchorEl);
  const iconSx: SxProps = {
    position: 'absolute',
    top: bar ? '60%' : '50%',
    left: '50%',
    transform: 'translate3d(-50%, -50%, 0)',
    fontSize: 60,
  };
  return (
    <>
      {isLoading && <Skeleton variant="rounded" animation="wave" width="100%" height="100%" />}
      {contentType === 'image' && (
        <>
          <img {...props} src={enableUrl(BASE_URL, src)} alt={alt} />
          {showPreviewButton && (
            <Fab
              color="primary"
              size="small"
              onClick={() => setShowPreview(true)}
              style={{ position: 'absolute', bottom: 8, right: 8 }}
            >
              <OpenInNew />
            </Fab>
          )}
          <ImagePreviewDialog
            isOpen={showPreview}
            toggleDialog={setShowPreview}
            imagePreview={enableUrl(BASE_URL, src)}
          />
        </>
      )}

      {contentType === 'other' && (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            borderRadius: 1,
            bgcolor: (t) => t.palette.background.innerGrey,
          }}
        />
      )}
      {contentType === 'other' && !remove && (
        <InsertDriveFile sx={{ ...iconSx, color: (t) => t.palette.grey[400] }} />
      )}
      {remove && <Delete sx={{ ...iconSx, color: (t) => t.palette.grey[700] }} />}
      {bar && (
        <ImageListItemBar
          title={alt}
          actionIcon={
            <IconButton size="small" onClick={handleClickMore} sx={{ color: '#fff' }}>
              <MoreVert />
            </IconButton>
          }
          position="top"
          sx={{ borderRadius: '4px 4px 0px 0px' }}
        />
      )}
      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        open={open}
        onClose={handleClose}
      >
        <MenuItem onClick={handleClickDownload}>
          <ListItemIcon>
            <Download />
          </ListItemIcon>
          <ListItemText>ダウンロード</ListItemText>
        </MenuItem>
        <MenuItem onClick={onRemove}>
          <ListItemIcon>
            <Delete />
          </ListItemIcon>
          <ListItemText>削除</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
}
