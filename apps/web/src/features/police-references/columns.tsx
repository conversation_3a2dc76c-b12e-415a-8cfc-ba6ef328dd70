import { isNullish } from 'common';

import type { BicycleColFactory } from '@/components/bicycles/columns';

export const referenceByCol: BicycleColFactory = () => ({
  field: 'policeReferenceBy',
  headerName: '照会内容',
  width: 200,
  valueGetter: (_, bicycle) => {
    const referenceBy = bicycle.policeReferenceBy;
    if (isNullish(referenceBy)) throw new Error('referenceBy is undefined');
    const { type, value } = referenceBy;
    switch (type) {
      case 'registrationNumber':
        return `防犯登録番号: ${value}`;
      case 'serialNumber':
        return `車体番号: ${value}`;
      default:
        throw new Error(type satisfies never);
    }
  },
});
