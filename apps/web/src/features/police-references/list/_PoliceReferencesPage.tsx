import { Paper, Stack, Typography } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';

import type { PoliceReferences } from 'lambda-api';
import { DataGrid } from 'mui-ex';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormat, datetimeFormatter } from '@/funcs/date';
import { getQueryKey } from '@trpc/react-query';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { useTextToPx } from '@/hooks/useTextToPx';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { policeReferenceListRoute } from '@/router/routes/keep/police-references';
import { ReferenceStatusChip } from '../common/ReferenceStatusChip';

type PoliceReference = PoliceReferences[number];
type ColDef = GridColDef<PoliceReference> & {
  field: keyof PoliceReference | keyof PoliceReference['_count'];
};

export const useColumns = () => {
  const { labels, policeStations } = useAppContext();
  const fit = useTextToPx();

  const statusCol: ColDef = {
    field: 'status',
    headerName: '照会ステータス',
    width: 160,
    valueGetter: (_, row) => labels.referenceStatus[row.status],
    renderCell: ({ row }) => <ReferenceStatusChip status={row.status} />,
  };
  const requestedAtCol: ColDef = {
    field: 'requestedAt',
    type: 'dateTime',
    headerName: '依頼日時',
    width: fit(datetimeFormat),
    valueGetter: (_, row) => new Date(row.requestedAt),
    valueFormatter: (value: Date) => datetimeFormatter(value),
  };
  const prefectureCol: ColDef = {
    field: 'prefectureCode',
    headerName: '都道府県',
    width: fit('都道府県'),
    valueGetter: (_, row) => {
      const police = policeStations.find((p) => p.prefectureCode === row.prefectureCode);
      if (police === undefined) throw new Error('都道府県が見つかりません');
      return police.prefecture;
    },
  };
  const requestsCol: ColDef = {
    field: 'requests',
    type: 'number',
    headerName: '依頼台数',
    width: fit('依頼台数'),
    valueGetter: (_, row) => row._count.requests,
  };
  const respondedAtCol: ColDef = {
    field: 'respondedAt',
    type: 'dateTime',
    headerName: '回答日時',
    width: fit(datetimeFormat),
    valueGetter: (_, row) => (row.respondedAt ? new Date(row.respondedAt) : undefined),
    valueFormatter: (value: Date | undefined) => (value ? datetimeFormatter(value) : '-'),
  };
  const responsesCol: ColDef = {
    field: 'responses',
    type: 'number',
    headerName: '回答台数',
    width: fit('依頼台数'),
    valueGetter: (_, row) => (row.respondedAt ? row._count.responses : '-'),
  };

  const memoCol: ColDef = {
    field: 'memo',
    headerName: labels.system.memo,
    width: 200,
  };

  return [
    statusCol,
    requestedAtCol,
    prefectureCol,
    requestsCol,
    respondedAtCol,
    responsesCol,
    memoCol,
  ];
};

const usePoliceReferenceExport = () => {
  const columns = useColumns();
  return useGridExport(columns, policeReferenceListRoute.meta.useTitle());
};

const Toolbar = () => {
  const exportFormatter = usePoliceReferenceExport();
  return (
    <DataGridToolbar
      to="/police-references/create"
      queryKey={getQueryKey(trpc.policeReferences.list)}
      exportFormatter={exportFormatter}
    />
  );
};

export const PoliceReferencesPage = () => {
  const { labels } = useAppContext();
  const columns = useColumns();
  const { data: agg } = trpc.bicycles.count.useQuery({ type: 'police-request' });
  const { data: references = [], isPending } = trpc.policeReferences.list.useQuery();
  const navigate = useNavigate();
  const title = policeReferenceListRoute.meta.useTitle();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/police-references/$id', params: { id: params.id.toString() } });
  };
  const count = agg?._count ?? 0;
  const statusText =
    count === 0
      ? `現在${labels.action.refer}可能な${labels.domain.bicycle}はありません。`
      : `現在照会可能な車両は${agg?._count}台です。`;
  return (
    <MainLayout scrollable title={title} maxWidth="lg">
      <Stack spacing={2} sx={{ height: 1 }}>
        <Typography variant="body2">{statusText}</Typography>
        <Stack component={Paper} sx={{ width: 1, flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            persistent="/police-references"
            columns={columns}
            rows={references}
            onRowClick={handleRowClick}
            disableRowSelectionOnClick
            slots={{ toolbar: Toolbar }}
            loading={isPending}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
              '& .MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
