import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogContentText,
  DialogTitle,
  Stack,
  TextField,
} from '@mui/material';
import React from 'react';

import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import type { PoliceReference } from 'lambda-api';

type Props = {
  reference: PoliceReference;
  onClose: () => void;
};

export const CancelDialog = ({ reference, onClose }: Props) => {
  const [memo, setMemo] = React.useState('');
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { mutateAsync } = trpc.policeReferences.cancel.useMutation({
    onSuccess: () => {
      const queryKey = getQueryKey(trpc.policeReferences.get, { id: reference.id });
      queryClient.invalidateQueries({ queryKey });
      enqueueSnackbar('警察照会依頼をキャンセルしました', { variant: 'success' });
      navigate({ to: '/police-references' });
    },
  });
  const handleCancel = async () => mutateAsync({ id: reference.id, memo });
  return (
    <>
      <DialogTitle>警察照会のキャンセル</DialogTitle>
      <DialogContent>
        <Stack spacing={2}>
          <DialogContentText>
            キャンセルすると各車両の照会依頼済みステータスが解除されます。
            <br />
            キャンセル後は元の状態には戻せません。
            もう一度照会を行う場合は警察照会を新規作成してください。
          </DialogContentText>
          <TextField
            label="備考 (キャンセル理由など)"
            value={memo}
            onChange={(e) => setMemo(e.target.value)}
            multiline
            rows={4}
          />
        </Stack>
      </DialogContent>
      <Stack alignItems="end" sx={{ px: 3, pb: 1 }}>
        <Stack direction="row" spacing={1}>
          <Button variant="outlined" onClick={onClose}>
            閉じる
          </Button>
          <Button variant="contained" onClick={handleCancel}>
            警察照会をキャンセルする
          </Button>
        </Stack>
      </Stack>
    </>
  );
};
