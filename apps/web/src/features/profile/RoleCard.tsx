import KeyValueListItem from '@/components/KeyValueListItem';
import { useAppContext } from '@/contexts/app-context';
import ProfileCard from './ProfileCard';

export default function RoleCard() {
  const { tenant, user } = useAppContext();
  return (
    <ProfileCard title="組織">
      <>
        <KeyValueListItem label="組織名" value={tenant.name} divider />
        <KeyValueListItem label="あなたのロール" value={user.role.name} />
      </>
    </ProfileCard>
  );
}
