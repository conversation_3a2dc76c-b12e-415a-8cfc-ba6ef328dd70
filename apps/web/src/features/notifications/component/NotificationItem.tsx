import React from 'react';

import { Close } from '@mui/icons-material';
import { Checkbox, IconButton, ListItem, ListItemText, Typography } from '@mui/material';

import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { RouterOutputs } from 'lambda-api';

import { useTimeout } from '@/hooks/useTimeout';

import { trpc } from '@/api';
import { datetimeFormatter } from '@/funcs/date';
import NotificationTypeChip from './NotificationTypeChip';

type NotificationOnUser = RouterOutputs['notifications']['list'][number];

type Props = {
  notification: NotificationOnUser;
};

const NotificationItem = ({
  notification: { id, title, description, type, readAt, createdAt },
}: Props) => {
  const [updated, setUpdated] = React.useState(false);
  const queryClient = useQueryClient();
  const onSuccess = () => {
    setUpdated(true);
    queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.notifications.list) });
  };

  // 1秒以内にリクエストが発生した場合は Cloudfront がキャッシュを返すため更新されたデータが取得できません。
  // これはユーザーが既読のチェックボックスを連打した場合などに発生します。
  // この問題を解決するため、同じ1秒後に再取得するようにしています。
  // ただし、更新されたデータが取得された場合は`NotificationItem` のキー設定により
  // コンポーネントはリセットされ、再取得のためのリクエストは発生しません。
  const { start } = useTimeout(onSuccess, 1);

  const read = trpc.notifications.read.useMutation();
  const hide = trpc.notifications.hide.useMutation();
  const handleCheck = (_: unknown, checked: boolean) => {
    read.mutate({ id, isRead: checked }, { onSuccess });
    start();
  };

  const handleOnDelete = () => {
    hide.mutate({ id, hide: true }, { onSuccess });
    start();
  };

  const isRead = readAt !== null;
  return (
    <ListItem sx={{ bgcolor: (t) => (isRead ? t.palette.background.innerGrey : undefined) }}>
      <ListItemText
        primary={
          <Typography variant="subtitle1" fontWeight={isRead ? 'normal' : 'bold'}>
            {title}
          </Typography>
        }
        secondary={
          <>
            <Typography variant="body2" color="text.primary" component="span">
              {description}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block">
              {datetimeFormatter(createdAt)}
            </Typography>
            <NotificationTypeChip
              type={type === 'user' || type === 'system' ? type : 'user'}
              size="small"
            />
          </>
        }
      />

      <Checkbox onChange={handleCheck} checked={isRead} disabled={updated} />
      <IconButton edge="end" aria-label="delete" onClick={handleOnDelete} disabled={updated}>
        <Close />
      </IconButton>
    </ListItem>
  );
};

export default NotificationItem;
