import { Assignment, Person } from '@mui/icons-material';
import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@mui/material';
import { useState } from 'react';

import { trpc } from '@/api';
import { isNullish } from 'common';
import type { Inquiry } from 'lambda-api';

type Props = {
  open: boolean;
  onClose: () => void;
  inquiry: Inquiry;
};

export default function AssignUserModal({ open, onClose, inquiry }: Props) {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(
    inquiry.assignUsers?.[0]?.userId || null,
  );

  const { data: users = [] } = trpc.users.list.useQuery();
  const assignMutation = trpc.inquiries.assign.useMutation({
    onSuccess: () => {
      onClose();
    },
  });

  const currentAssignee = inquiry.assignUsers?.[0]?.user;

  const handleAssign = async () => {
    await assignMutation.mutateAsync({
      inquiryId: inquiry.id,
      userId: selectedUserId,
    });
  };

  const handleUnassign = async () => {
    await assignMutation.mutateAsync({
      inquiryId: inquiry.id,
      userId: null,
    });
  };

  const isLoading = assignMutation.isPending;
  const hasChanges = selectedUserId !== (currentAssignee?.id || null);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Stack direction="row" spacing={1} alignItems="center">
          <Assignment />
          <Typography variant="h6">担当者の割り当て</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <Typography variant="body2" color="text.secondary">
            問い合わせ: {inquiry.title}
          </Typography>

          {currentAssignee && (
            <Stack spacing={1}>
              <Typography variant="subtitle2">現在の担当者</Typography>
              <Stack direction="row" spacing={1} alignItems="center">
                <Person fontSize="small" />
                <Typography variant="body2">
                  {currentAssignee.displayName || currentAssignee.name}
                </Typography>
              </Stack>
            </Stack>
          )}

          <FormControl fullWidth>
            <InputLabel>担当者を選択</InputLabel>
            <Select
              value={selectedUserId || ''}
              onChange={(e) => setSelectedUserId(e.target.value || null)}
              label="担当者を選択"
              disabled={isLoading}
            >
              <MenuItem value="">
                <em>未割り当て</em>
              </MenuItem>
              {users.map((user) => (
                <MenuItem key={user.id} value={user.id}>
                  {user.displayName || user.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={isLoading}>
          キャンセル
        </Button>

        {currentAssignee && (
          <Button onClick={handleUnassign} disabled={isLoading} color="warning" variant="outlined">
            割り当て解除
          </Button>
        )}

        <Button onClick={handleAssign} disabled={isLoading || !hasChanges} variant="contained">
          {isNullish(selectedUserId) ? '割り当て解除' : '割り当て'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
