import { RouterButton } from '@/components/RouterButton';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit, Person } from '@mui/icons-material';
import { Chip, Paper, Stack, Typography } from '@mui/material';
import type { Inquiry, InquiryComments } from 'lambda-api';
import { useForm } from 'react-hook-form';
import {
  type InquiryState,
  InquiryStateSchema,
  RhfInquiry,
  inquiryToState,
} from '../common/RhfInquiry';

import InquiryCommentForm from './comments/InquiryCommentForm';
import InquiryCommentList from './comments/InquiryCommentList';

type Props = {
  inquiry: Inquiry;
  comments: InquiryComments;
};

export default function InquiryDetails({ inquiry, comments }: Props) {
  const { labels } = useAppContext();

  const { control, watch } = useForm<InquiryState>({
    defaultValues: inquiryToState(inquiry),
    resolver: zodResolver(InquiryStateSchema),
  });

  const currentAssignee = inquiry.assignUsers?.[0]?.user;

  return (
    <Stack spacing={3}>
      <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
        <RhfInquiry control={control} watch={watch} readOnly />

        <Stack spacing={2}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="body2" fontWeight="medium">
              担当者:
            </Typography>
            {currentAssignee ? (
              <Chip
                icon={<Person />}
                label={currentAssignee.displayName || currentAssignee.name}
                size="small"
                variant="outlined"
              />
            ) : (
              <Typography variant="body2" color="text.secondary">
                未割り当て
              </Typography>
            )}
          </Stack>

          <Stack direction="row" spacing={2} alignItems="center" justifyContent="end">
            <RouterButton to="/inquiries/$id/edit" params={{ id: inquiry.id }} startIcon={<Edit />}>
              {labels.action.edit}
            </RouterButton>
          </Stack>
        </Stack>
      </Stack>

      <InquiryCommentList comments={comments} />
      <InquiryCommentForm inquiryId={inquiry.id} />
    </Stack>
  );
}
