# PRのレビュー

このプロンプトは日本語を使用してください。
以下にPRのレビューの手順を示します。

## 1. GitHub Copilotのモード確認

あなたが現在利用可能なツールに、Git操作系（get_changed_filesなど）が含まれているかどうかを確認してください。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれていないケース

ユーザーにGitHub CopilotをAgentモードに切り替えることを伝えて処理を中断します。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれているケース

処理を続行してください。

## 2. デフォルトブランチと現在のブランチの差分を確認

以下のコマンドで差分を確認し、どのような変更が行われたかを把握してください。

```bash
git fetch && \
git remote set-head origin -a && \
git diff origin/HEAD
```

<!--
以下はコマンドの解説です。
- git fetch : リモートの最新情報を取得します。
- git remote set-head origin -a : リモートのデフォルトブランチをローカルに設定します。
- git diff origin/HEAD : デフォルトブランチと現在のブランチの差分を表示します。
-->

## 3. レビュー

### レビューの観点

関数型プログラミングとReactのベストプラクティスとアンチパターンを考慮してレビューしてください。

### レビューの形式

レビューは次のルールを守ってください。

- 指摘箇所のファイルのパス、行番号を示す
- 指摘の前後のコードをコードブロックで囲む（部分的に省略してもよい）
- 指摘内容の明確な説明
- 可能であればベストプラクティスに基づいた改善案の提示（コードブロックで囲む）
