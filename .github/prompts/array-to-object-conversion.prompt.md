# 配列データからオブジェクト変換プロンプト

## 概要
key-valueペアを持つ配列データを、シンプルなオブジェクトリテラル形式に変換するプロンプトです。

## 使用例

### 入力データ形式
```typescript
const data = [
  { key: 'user', value: 'ユーザー', groups: ['Basic'] },
  { key: 'system', value: 'システム', groups: ['Basic'] },
  // ...
];
```

### 出力形式
```typescript
const data = {
  user: 'ユーザー',
  system: 'システム',
  // ...
} as const;
```

## プロンプト

指示された配列データを `Record<string, string>` 形式のオブジェクトに変換してください。

- `key` プロパティをオブジェクトのプロパティキーとする
- `value` プロパティをオブジェクトのプロパティ値とする
- その他のプロパティ（`groups`等）は除外する
- `as const` を付与して型を固定する
- コピペで使用できるコードブロック形式で出力する

## 注意事項
- 関数やマッピング処理は不要、オブジェクトリテラルのコードそのものを出力
- TypeScript形式で出力
- 型安全性を確保するため `as const` を使用
