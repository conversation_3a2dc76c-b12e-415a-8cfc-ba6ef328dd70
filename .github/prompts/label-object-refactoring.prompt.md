# ラベルオブジェクト辞書化プロンプト

## 概要
ハードコーディングされたラベル文字列を、複数の辞書から動的に組み合わせる形式に修正するプロンプトです。
ラベルの統一性と保守性を向上させることが目的です。

## 対象ファイル
`packages/models/src/label.ts`

## 修正パターン

### 基本的な置き換え
```typescript
// Before: ハードコーディング
const example = {
  field: '車両区分',
  action: '編集',
};

// After: 辞書参照
const createExampleLabels = (dict: Dict) => ({
  field: dict.da.bicycle,
  action: dict.action.edit,
});
```

### 複合ラベルの組み合わせ
```typescript
// Before: 固定文字列
const example = {
  editVehicle: '車両情報の編集',
  cancelSell: '売却キャンセル',
};

// After: 辞書組み合わせ
const createExampleLabels = (dict: Dict) => ({
  editVehicle: `${dict.da.bicycle}${dict.common.info}の${dict.action.edit}`,
  cancelSell: `${dict.eventType.sell}${dict.action.cancel}`,
});
```

## 利用可能な辞書グループ

### 基本辞書 (dict)
- `dict.common.*` - 共通用語（料金、価格、期限、情報など）
- `dict.location.*` - 住所関連（郵便番号、住所、都道府県など）
- `dict.web.*` - WEB関連（ユーザー、システム、ステータスなど）
- `dict.contact.*` - 連絡先関連（メール、電話、FAXなど）
- `dict.org.*` - 組織関連（組織、部署、担当者など）
- `dict.state.*` - 状態関連（有無、完了、存在など）
- `dict.da.*` - ドメイン固有（車両、所有者、陸運局など）
- `dict.action.*` - アクション（登録、編集、キャンセルなど）
- `dict.eventType.*` - イベント種別（発見、売却、廃棄など）

### 定数ラベル (constantLabels)
- `constantLabels.new` - '新規'
- `constantLabels.police` - '警察'
- `constantLabels.postalCode` - '郵便番号'
- `constantLabels.prefecture` - '都道府県'
- `constantLabels.city` - '市区町村'

## 作業手順

1. **現在のファイルを確認**
   - 修正対象のラベルオブジェクトを特定
   - 既存の辞書定義を確認

2. **辞書の洗い出し**
   - 修正対象の文字列を分析
   - 既存の辞書で対応可能な部分を特定
   - 新しく必要な単語を洗い出し

3. **新しい単語の確認**
   - 辞書にない単語は事前にユーザーに確認
   - 適切なグループ（common, domain, action等）を提案
   - ユーザーの承認後に辞書を更新

4. **ラベルオブジェクトの修正**
   - `const createXxxLabels = (dict: Dict) => ({ ... })` 形式に変更
   - 辞書参照と文字列テンプレートで組み合わせ
   - 型安全性を確保（`satisfies` 句の維持）

5. **呼び出し側の更新**
   - `getLabelObject`関数内で新しい関数を呼び出し
   - 戻り値に追加

## 修正例

### Before
```typescript
const bicycleEventTypeLabels = {
  find: '発見',
  editLocation: '位置情報の編集',
  cancelSell: '売却キャンセル',
  requestPolice: '警察照会',
} as const satisfies Record<BicycleEventType, string>;
```

### After
```typescript
const createBicycleEventTypeLabels = (dict: Dict) => ({
  find: dict.eventType.find,
  editLocation: `${dict.da.location}の${dict.action.edit}`,
  cancelSell: `${dict.eventType.sell}${dict.action.cancel}`,
  requestPolice: `${constantLabels.police}${dict.action.refer}`,
}) as const satisfies Record<BicycleEventType, string>;
```

## 注意事項

- **型安全性の維持**: `satisfies` 句を削除しない
- **段階的修正**: 大きなオブジェクトは部分的に修正を進める
- **既存機能の保持**: 修正後も同じラベル文字列が生成されることを確認
- **辞書の一貫性**: 新しい単語は適切なグループに配置
- **コメントの管理**: 修正完了後はコメントを削除

## プロンプト

指定されたラベルオブジェクトについて、ハードコーディングされた文字列を辞書参照による動的生成に修正してください。

1. 現在のファイル内容を確認
2. 既存辞書で対応可能な部分と新規追加が必要な単語を分析
3. 新規単語が必要な場合は適切なグループを提案して確認
4. `const createXxxLabels = (dict: Dict) => ({ ... })` 形式で修正
5. 型安全性と既存機能を維持

修正により、ラベルの統一性と保守性が向上します。
