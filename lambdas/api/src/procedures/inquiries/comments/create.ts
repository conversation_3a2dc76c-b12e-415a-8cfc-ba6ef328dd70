import { ImageInputSchema } from 'common';
import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryCommentMutation = async ({
  input: { inquiryId, commentId, body, images },
  ctx: { prisma, userId },
}: MutationArgs) => {
  await prisma.inquiryComment.create({
    data: {
      id: commentId,
      inquiryId,
      userId,
      body,
      images: { createMany: { data: images } },
    },
  });

  return 'OK';
};

export const createInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(createInquiryCommentMutation);
