import type { z } from 'zod';

import { getJpFiscal<PERSON>ear, isNullish } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

const createInquiryAssignmentNotification = async (
  tx: any,
  data: {
    inquiryId: string;
    assigneeId: string;
    assignerId: string;
    title: string;
    description: string;
    isUnassign?: boolean;
  },
) => {
  const notification = await tx.notification.create({
    data: {
      title: data.title,
      description: `${data.description} [inquiry:${data.inquiryId}]`,
      type: 'user',
      scope: 'all',
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.isUnassign ? data.assignerId : data.assigneeId,
    },
  });
};

export const updateInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: updaterId } = ctx;
  const { id, receivedAt, assignUserId } = input;

  await asyncTx(async (tx) => {
    // Get current inquiry to check assignment changes
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id },
      include: { assignUsers: { include: { user: true } } },
    });

    const currentAssignee = currentInquiry.assignUsers?.[0]?.user;
    const isAssigning = !isNullish(assignUserId) && isNullish(currentAssignee);
    const isUnassigning = isNullish(assignUserId) && !isNullish(currentAssignee);
    const isReassigning =
      !isNullish(assignUserId) &&
      !isNullish(currentAssignee) &&
      assignUserId !== currentAssignee.id;

    const data: Prisma.InquiryCreateInput = {
      id,
      title: input.title,
      description: input.description,
      address: input.address,
      memo: input.memo,
      receivedAt: input.receivedAt,
      fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
      receptionRoute: { connect: { id: input.receptionRouteId } },
      receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
      images: { createMany: { data: input.images } },
      assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
    };

    // Update the main inquiry
    await tx.inquiry.update({
      where: { id },
      data: {
        ...data,
        assignUsers: assignUserId
          ? { deleteMany: {}, create: { userId: assignUserId } }
          : { deleteMany: {} },
      },
    });

    // Create version
    const { id: _, ...versionData } = data;
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Handle notifications for assignment changes
    if (isAssigning && assignUserId !== updaterId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId: id,
        assigneeId: assignUserId,
        assignerId: updaterId,
        title: '問い合わせが割り当てられました',
        description: `問い合わせ「${input.title}」があなたに割り当てられました。`,
      });
    }

    if (isUnassigning && currentAssignee && currentAssignee.id !== updaterId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId: id,
        assigneeId: currentAssignee.id,
        assignerId: updaterId,
        title: '問い合わせの割り当てが解除されました',
        description: `問い合わせ「${input.title}」の割り当てが解除されました。`,
        isUnassign: true,
      });
    }

    if (isReassigning) {
      if (currentAssignee.id !== updaterId) {
        await createInquiryAssignmentNotification(tx, {
          inquiryId: id,
          assigneeId: currentAssignee.id,
          assignerId: updaterId,
          title: '問い合わせの割り当てが変更されました',
          description: `問い合わせ「${input.title}」の割り当てが変更されました。`,
          isUnassign: true,
        });
      }

      if (assignUserId !== updaterId) {
        await createInquiryAssignmentNotification(tx, {
          inquiryId: id,
          assigneeId: assignUserId,
          assignerId: updaterId,
          title: '問い合わせが割り当てられました',
          description: `問い合わせ「${input.title}」があなたに割り当てられました。`,
        });
      }
    }
  });

  return 'OK';
};

export const updateInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(updateInquiryMutation);
