import { ImageInputSchema } from 'common';
import { z } from 'zod';

export const InquiryInputSchema = z
  .object({
    id: z.string().uuid(),
    receptionRouteId: z.string(),
    receiverId: z.string().nullable(),
    receivedAt: z.date().nullable(),
    title: z.string(),
    description: z.string(),
    address: z.string(),
    memo: z.string().nullable(),
    images: z.array(ImageInputSchema),
    assignUserId: z.string().optional(),
  })
  .strict();
