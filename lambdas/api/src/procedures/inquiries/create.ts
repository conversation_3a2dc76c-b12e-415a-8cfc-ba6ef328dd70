import type { z } from 'zod';

import { getJpFiscalYear, isNullish } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

const createInquiryAssignmentNotification = async (
  tx: any,
  data: {
    inquiryId: string;
    assigneeId: string;
    assignerId: string;
    title: string;
    description: string;
  },
) => {
  const notification = await tx.notification.create({
    data: {
      title: data.title,
      description: `${data.description} [inquiry:${data.inquiryId}]`,
      type: 'user',
      scope: 'all',
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.assigneeId,
    },
  });
};

export const createInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: creatorId } = ctx;
  const { id, receivedAt, assignUserId } = input;

  const data: Prisma.InquiryCreateInput = {
    id,
    title: input.title,
    description: input.description,
    address: input.address,
    memo: input.memo,
    receivedAt: input.receivedAt,
    fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
    receptionRoute: { connect: { id: input.receptionRouteId } },
    receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
    images: { createMany: { data: input.images } },
    assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
  };

  await asyncTx(async (tx) => {
    await tx.inquiry.create({ data: { ...data, id } });
    const { id: _, ...versionData } = data; // remove id from payload, then Prisma will auto-generate new uuid
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Create notification if assigning to different user
    if (assignUserId && assignUserId !== creatorId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId: id,
        assigneeId: assignUserId,
        assignerId: creatorId,
        title: '問い合わせが割り当てられました',
        description: `問い合わせ「${input.title}」があなたに割り当てられました。`,
      });
    }
  });

  return 'OK';
};

export const createInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(createInquiryMutation);
