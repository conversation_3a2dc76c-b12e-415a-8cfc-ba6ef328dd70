import { z } from 'zod';

import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import {
  analyzeAssignmentChange,
  createAssignmentUpdateData,
  handleAssignmentNotifications,
} from './assignment-utils';

const AssignInputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    userId: z.string().uuid().nullable(),
  })
  .strict();

type Input = z.infer<typeof AssignInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const assignInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { inquiryId, userId } = input;
  const { asyncTx, userId: assignerId } = ctx;

  await asyncTx(async (tx) => {
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id: inquiryId },
      include: {
        assignUsers: { include: { user: true } },
        images: { orderBy: { sortOrder: 'asc' } },
      },
    });

    const currentAssignee = currentInquiry.assignUsers?.[0]?.user;
    const change = analyzeAssignmentChange(currentAssignee?.id, userId, assignerId);

    if (change.type === 'no-change') {
      return 'OK';
    }

    const versionData: Prisma.InquiryCreateInput = {
      title: currentInquiry.title,
      description: currentInquiry.description,
      address: currentInquiry.address,
      memo: currentInquiry.memo,
      receivedAt: currentInquiry.receivedAt,
      fiscalYear: currentInquiry.fiscalYear,
      receptionRoute: currentInquiry.receptionRouteId
        ? { connect: { id: currentInquiry.receptionRouteId } }
        : undefined,
      receiver: currentInquiry.receiverId
        ? { connect: { id: currentInquiry.receiverId } }
        : undefined,
      assignUsers: userId ? { create: { userId } } : undefined,
      images: { createMany: { data: currentInquiry.images } },
      original: { connect: { id: inquiryId } },
    };

    await tx.inquiry.update({
      where: { id: inquiryId },
      data: {
        assignUsers: createAssignmentUpdateData(userId),
      },
    });

    await tx.inquiry.create({ data: versionData });

    await handleAssignmentNotifications(tx, change, inquiryId, currentInquiry.title, assignerId);
  });

  return 'OK';
};

export const assignInquiry = (p: TRPCProcedure) =>
  p.input(AssignInputSchema).mutation(assignInquiryMutation);
