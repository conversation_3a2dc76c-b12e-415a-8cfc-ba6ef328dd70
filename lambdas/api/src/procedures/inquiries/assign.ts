import { z } from 'zod';

import { isNullish } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';

const AssignInputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    userId: z.string().uuid().nullable(),
  })
  .strict();

type Input = z.infer<typeof AssignInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

const createInquiryAssignmentNotification = async (
  tx: any,
  data: {
    inquiryId: string;
    assigneeId: string;
    assignerId: string;
    title: string;
    description: string;
    isUnassign?: boolean;
  },
) => {
  const notification = await tx.notification.create({
    data: {
      title: data.title,
      description: data.description,
      type: 'user',
      scope: 'all',
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.isUnassign ? data.assignerId : data.assigneeId,
    },
  });
};

export const assignInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { inquiryId, userId } = input;
  const { asyncTx, userId: assignerId } = ctx;

  await asyncTx(async (tx) => {
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id: inquiryId },
      include: {
        assignUsers: { include: { user: true } },
        images: { orderBy: { sortOrder: 'asc' } },
      },
    });

    const currentAssignee = currentInquiry.assignUsers?.[0]?.user;
    const isAssigning = !isNullish(userId);
    const isUnassigning = isNullish(userId) && !isNullish(currentAssignee);
    const isReassigning =
      !isNullish(userId) && !isNullish(currentAssignee) && userId !== currentAssignee.id;
    const isSameUser =
      !isNullish(userId) && !isNullish(currentAssignee) && userId === currentAssignee.id;

    if (isSameUser) {
      return 'OK';
    }

    const versionData: Prisma.InquiryCreateInput = {
      title: currentInquiry.title,
      description: currentInquiry.description,
      address: currentInquiry.address,
      memo: currentInquiry.memo,
      receivedAt: currentInquiry.receivedAt,
      fiscalYear: currentInquiry.fiscalYear,
      receptionRoute: currentInquiry.receptionRouteId
        ? { connect: { id: currentInquiry.receptionRouteId } }
        : undefined,
      receiver: currentInquiry.receiverId
        ? { connect: { id: currentInquiry.receiverId } }
        : undefined,
      assignUsers: isAssigning ? { create: { userId } } : undefined,
      images: { createMany: { data: currentInquiry.images } },
      original: { connect: { id: inquiryId } },
    };

    await tx.inquiry.update({
      where: { id: inquiryId },
      data: {
        assignUsers: isAssigning ? { deleteMany: {}, create: { userId } } : { deleteMany: {} },
      },
    });

    await tx.inquiry.create({ data: versionData });

    if (isAssigning && userId !== assignerId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId,
        assigneeId: userId,
        assignerId,
        title: '問い合わせが割り当てられました',
        description: `問い合わせ「${currentInquiry.title}」があなたに割り当てられました。`,
      });
    }

    if (isUnassigning && currentAssignee && currentAssignee.id !== assignerId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId,
        assigneeId: currentAssignee.id,
        assignerId,
        title: '問い合わせの割り当てが解除されました',
        description: `問い合わせ「${currentInquiry.title}」の割り当てが解除されました。`,
        isUnassign: true,
      });
    }

    if (isReassigning) {
      if (currentAssignee.id !== assignerId) {
        await createInquiryAssignmentNotification(tx, {
          inquiryId,
          assigneeId: currentAssignee.id,
          assignerId,
          title: '問い合わせの割り当てが変更されました',
          description: `問い合わせ「${currentInquiry.title}」の割り当てが変更されました。`,
          isUnassign: true,
        });
      }

      if (userId !== assignerId) {
        await createInquiryAssignmentNotification(tx, {
          inquiryId,
          assigneeId: userId,
          assignerId,
          title: '問い合わせが割り当てられました',
          description: `問い合わせ「${currentInquiry.title}」があなたに割り当てられました。`,
        });
      }
    }
  });

  return 'OK';
};

export const assignInquiry = (p: TRPCProcedure) =>
  p.input(AssignInputSchema).mutation(assignInquiryMutation);
