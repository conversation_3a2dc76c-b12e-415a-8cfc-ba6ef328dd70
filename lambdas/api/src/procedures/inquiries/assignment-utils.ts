import { isNullish } from 'common';

export type AssignmentChange = {
  type: 'assign' | 'unassign' | 'reassign' | 'no-change';
  currentAssigneeId?: string;
  newAssigneeId?: string;
  shouldNotifyCurrentAssignee: boolean;
  shouldNotifyNewAssignee: boolean;
};

export type NotificationData = {
  inquiryId: string;
  inquiryTitle: string;
  assigneeId: string;
  assignerId: string;
  type: 'assign' | 'unassign' | 'reassign-old' | 'reassign-new';
};

/**
 * Analyzes assignment changes and determines what notifications should be sent
 */
export const analyzeAssignmentChange = (
  currentAssigneeId: string | null | undefined,
  newAssigneeId: string | null | undefined,
  actorId: string,
): AssignmentChange => {
  const hasCurrentAssignee = !isNullish(currentAssigneeId);
  const hasNewAssignee = !isNullish(newAssigneeId);
  const isSameAssignee = currentAssigneeId === newAssigneeId;

  if (isSameAssignee) {
    return {
      type: 'no-change',
      currentAssigneeId: currentAssigneeId || undefined,
      newAssigneeId: newAssigneeId || undefined,
      shouldNotifyCurrentAssignee: false,
      shouldNotifyNewAssignee: false,
    };
  }

  if (!hasCurrentAssignee && hasNewAssignee) {
    return {
      type: 'assign',
      newAssigneeId,
      shouldNotifyCurrentAssignee: false,
      shouldNotifyNewAssignee: newAssigneeId !== actorId,
    };
  }

  if (hasCurrentAssignee && !hasNewAssignee) {
    return {
      type: 'unassign',
      currentAssigneeId,
      shouldNotifyCurrentAssignee: currentAssigneeId !== actorId,
      shouldNotifyNewAssignee: false,
    };
  }

  if (hasCurrentAssignee && hasNewAssignee) {
    return {
      type: 'reassign',
      currentAssigneeId,
      newAssigneeId,
      shouldNotifyCurrentAssignee: currentAssigneeId !== actorId,
      shouldNotifyNewAssignee: newAssigneeId !== actorId,
    };
  }

  return {
    type: 'no-change',
    shouldNotifyCurrentAssignee: false,
    shouldNotifyNewAssignee: false,
  };
};

/**
 * Creates a notification for inquiry assignment changes
 */
export const createInquiryAssignmentNotification = async (tx: any, data: NotificationData) => {
  const messages = {
    assign: {
      title: '問い合わせが割り当てられました',
      description: `問い合わせ「${data.inquiryTitle}」があなたに割り当てられました。`,
    },
    unassign: {
      title: '問い合わせの割り当てが解除されました',
      description: `問い合わせ「${data.inquiryTitle}」の割り当てが解除されました。`,
    },
    'reassign-old': {
      title: '問い合わせの割り当てが変更されました',
      description: `問い合わせ「${data.inquiryTitle}」の割り当てが変更されました。`,
    },
    'reassign-new': {
      title: '問い合わせが割り当てられました',
      description: `問い合わせ「${data.inquiryTitle}」があなたに割り当てられました。`,
    },
  };

  const message = messages[data.type];

  const notification = await tx.notification.create({
    data: {
      title: message.title,
      description: `${message.description} [inquiry:${data.inquiryId}]`,
      type: 'user',
      scope: 'all',
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.assigneeId,
    },
  });
};

/**
 * Handles all assignment notifications based on the change analysis
 */
export const handleAssignmentNotifications = async (
  tx: any, // TODO
  change: AssignmentChange,
  inquiryId: string,
  inquiryTitle: string,
  actorId: string,
) => {
  const notifications: Promise<void>[] = [];

  if (change.shouldNotifyCurrentAssignee && change.currentAssigneeId) {
    const notificationType = change.type === 'unassign' ? 'unassign' : 'reassign-old';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.currentAssigneeId,
        assignerId: actorId,
        type: notificationType,
      }),
    );
  }

  if (change.shouldNotifyNewAssignee && change.newAssigneeId) {
    const notificationType = change.type === 'assign' ? 'assign' : 'reassign-new';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.newAssigneeId,
        assignerId: actorId,
        type: notificationType,
      }),
    );
  }

  await Promise.all(notifications);
};

/**
 * Creates Prisma assignment data for inquiry creation/update
 */
export const createAssignmentData = (assignUserId?: string) => {
  if (!assignUserId) return undefined;
  return { create: { userId: assignUserId } };
};

/**
 * Creates Prisma assignment update data for inquiry updates
 */
export const createAssignmentUpdateData = (assignUserId?: string | null) => {
  return assignUserId ? { deleteMany: {}, create: { userId: assignUserId } } : { deleteMany: {} };
};
