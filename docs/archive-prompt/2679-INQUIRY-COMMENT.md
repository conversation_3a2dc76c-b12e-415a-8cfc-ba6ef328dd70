This is the original prompt for the AI agent use for PR: https://github.com/oec-tokyo/maphin/pull/2679

# Prompt for AI agent
📘 Project context:
  This project called "maphin" handles the task creation flow in a multi-tenant SaaS platform.
  It includes a form using React Hook Form + Zod for validation, and uses React Query for submitting to a backend API.

  📦 Stack:
  React + TypeScript + React Hook Form + Zod + React Query + Material UI + Prisma(Nodejs + Postgres sql), Material UI

  📦 Local execute:
  - MacBook 2020 Intel core i5 chip.
  - Volta for npm version manager
  - profile : maphin
  - environment: xcong ( other env is fuka, stage, prod, just use xcong is enough)

Note:
1. understand and clearly about my project and coding rule and proposal best practice code match with project context even i was specify step to you.
2. understand about task which assignment for you.


📘 Task context:
- Inquiry has comments (`InquiryComment`) with optional images (`InquiryCommentImage`) ( reference: packages/database/prisma/schema/inquiry.prisma)
- Inquiry detail is loaded via `trpc.inquiries.get.useQuery({ id })`
- Inquiry schema is versioned; comments are loaded under `inquiry.comments`
- Current frontend component is `InquiryDetailsPage` → `InquiryDetails` → `RhfInquiry`

🎯 Goal:
Add full support for comment viewing and posting on the inquiry detail page.(like github issues page)
The comment should be handled in its own API (separate from `inquiries.get`), with React Query support.

🔧 Implementation Plan:
1. API: Create TRPC endpoint to list comments
   - Add procedure: `trpc.inquiries.comments.list`
   - Accept `inquiryId` as input
   - Return list of `InquiryComment` with `user` (author) and `images`
   - Use `prisma.inquiryComment.findMany({ where: { inquiryId, tenantId }, ... })`

2. API: Create TRPC mutation to post comment
   - Procedure: `trpc.inquiries.comments.create`
   - Input: `{ inquiryId: string, body }`
   - Authenticated user will be used as `userId`
   - Save to `InquiryComment` table, optionally support images later

3. Frontend: Add InquiryCommentList component
   - Use `trpc.inquiries.comments.list.useQuery({ inquiryId })`
   - Render list with avatar, author name, comment body, timestamp, and images
   - Use MUI components: `Card`, `CardHeader`, `Avatar`, `CardContent`, etc...

4. Frontend: Add InquiryCommentForm component
   - Use React Hook Form + Zod for validation
   - Post with `trpc.inquiries.comments.create.useMutation()`
   - On success, invalidate `comments.list` query to refetch
   - Use MUI `TextField`, `Button`

5. Compose in InquiryDetails.tsx
   - After `<RhfInquiry readOnly />`, render:
     ```tsx
     <InquiryCommentList inquiryId={inquiry.id} />
     <InquiryCommentForm inquiryId={inquiry.id} />
     ```

### 🧩 What AugmentCode will do:

* Generate backend procedures:

  * `/lambdas/api/src/procedures/inquiries/comments/list.ts`
  * `/lambdas/api/src/procedures/inquiries/comments/create.ts`
* Update your router file to include them
* Generate frontend hooks using `trpc.inquiries.comments.*`
* Generate new components:

  * `/features/inquiries/details/components/InquiryCommentList.tsx`
  * `/features/inquiries/details/components/InquiryCommentForm.tsx`
* Update `/features/inquiries/details/InquiryDetails.tsx` to use them


# IMPLEMENTATION PLAN

## Task 1: Create TRPC API to list comments
- [x] Create `lambdas/api/src/procedures/inquiries/comments/list.ts`
  [x] Implemented with proper input validation using Zod
  [x] Query includes user and images with proper filtering (deleted: false)
  [x] Ordered by createdAt ascending for chronological display
  [x] Return type: `InquiryCommentWithUser[]`

## Task 2: Create TRPC API to create comments
- [x] Create `lambdas/api/src/procedures/inquiries/comments/create.ts`
  [x] Input validation: `{ inquiryId: string, body: string }` with max 1000 chars
  [x] Mutation creates new `InquiryComment` with authenticated `userId`
  [x] Returns created comment with user data included
  [x] Proper tenant isolation with `tenantId`

## Task 3: Update TRPC router to include comment procedures
- [x] Update `lambdas/api/src/trpc.ts`
  [x] Imported comment procedures: `createInquiryComment`, `listInquiryComments`
  [x] Added to inquiries router: `comments: { list: listInquiryComments(procedure), create: createInquiryComment(procedure) }`
  [x] Now available as `trpc.inquiries.comments.list` and `trpc.inquiries.comments.create`

## Task 4: Create InquiryCommentList component
- [x] Create `apps/web/src/features/inquiries/details/components/InquiryCommentList.tsx`
  [x] Uses `trpc.inquiries.comments.list.useQuery({ inquiryId })` with proper loading states
  [x] Renders with MUI `Card`, `CardHeader`, `Avatar`, `CardContent` components
  [x] Displays: user avatar (first letter), name, comment body, formatted timestamp
  [x] Handles loading, error, and empty states with appropriate messages
  [x] Shows comment count in header and preserves line breaks with `whiteSpace: 'pre-wrap'`
  [x] Placeholder for future image display functionality

## Task 5: Create InquiryCommentForm component
- [x] Create `apps/web/src/features/inquiries/details/components/InquiryCommentForm.tsx`
  [x] Uses React Hook Form + Zod validation with proper error handling
  [x] Schema validation: 1-1000 characters with Japanese error messages
  [x] Uses `trpc.inquiries.comments.create.useMutation()` with error handling
  [x] On success: invalidates comments list query + resets form automatically
  [x] MUI `TextField` (multiline, 4 rows) + `Button` with loading states
  [x] Proper form submission with disabled states during pending operations

## Task 6: Integrate components into InquiryDetails
- [x] Update `apps/web/src/features/inquiries/details/InquiryDetails.tsx`
  [x] Imported comment components: `InquiryCommentList`, `InquiryCommentForm`
  [x] Added after `<RhfInquiry />`: comment list and form in proper order
  [x] Proper spacing and layout with MUI `Stack` (spacing={3})
  [x] Maintains existing inquiry details layout while adding comment functionality
## Task 7: Support image upload in comment form
- [x] Use `RhfMultiImageSelector` (from `apps/web/src/features/inquiries/common/RhfInquiry.tsx`) in `InquiryCommentForm`
  [x] Created S3 key generation: Added `createInquiryCommentImagesKey` function in `packages/models/src/image.ts`
  [x] Created presigned URL API: Added `lambdas/api/src/procedures/presigned-urls/inquiry-comment.ts`
  [x] Updated TRPC router: Added `presignedUrls.inquiry.comment.list` endpoint
  [x] Updated comment creation API: Modified `create.ts` to accept and handle `images` array
  [x] Created upload utility: Added `uploadInquiryCommentImagesToS3` function in `funcs.ts`
  [x] Updated comment form: Added `RhfMultiImageSelector` with max 5 images like others
  [x] Form validation: Updated schema to include `images: z.array(ImageSchema)`
  [x] S3 upload integration: Images uploaded before comment creation with proper error handling

## Task 8: Display uploaded comment images
- [x] Update `InquiryCommentList.tsx` to show images for each comment
  [x] Created image component: Added `InquiryCommentImages.tsx` using project's `Thumbnails` component
  [x] Integrated into comment list: Images displayed below comment body when available
  [x] Modal preview functionality: Uses existing `ImagePreviewDialog` with swiper navigation
  [x] Responsive design: Small thumbnails with 3-column minimum layout
  [x] Graceful handling: Component returns null when no images exist
  [x] Consistent styling: Follows project's image display patterns

# 🎉 FINAL IMPLEMENTATION COMPLETE

All tasks including image upload and display functionality have been successfully implemented!

## [x] Complete Feature Set

### Backend (TRPC API)
- `trpc.inquiries.comments.list` - Lists comments with user data and images
- `trpc.inquiries.comments.create` - Creates comments with image support
- `trpc.presignedUrls.inquiry.comment.list`
  - Generates S3 upload URLs for comment images
  - Format of s3-key: ${s3TenantsPrefix}/${tenantId}/inquiries/${inquiryId}/comments/${commentId}/user-${userId}/images/${dateString}/${filename}` as const;

### Frontend Components
- Integrated into `InquiryDetails` page with centralized data loading
- `InquiryCommentList`
  - Displays comments with images and modal preview
  - Thumbnail display with modal preview functionality
- `InquiryCommentForm`
  - Form with text input and image upload (max ５ images)
  - Send payload to backend if validate pass:
    - Use mutateAsync to async to backend.
    - Show error if create fail
    - Able visual new comment so no need show snackbar after create successful
  - Use Mui Loading button to spin when Submitting

### Key Features Implemented
- [x] Comment CRUD: View and post comments with proper validation
- [x] Image Upload: Multi-image upload with S3 integration (max ５ per comment)
- [x] Image Display: Thumbnail grid with modal preview and swiper navigation
- [x] Real-time Updates: Comments refresh automatically after posting
- [x] Centralized Loading: Single loading spinner for better UX
- [x] Error Handling: Comprehensive error states and user feedback
- [x] Responsive Design: Mobile-friendly layout with proper spacing
- [x] Type Safety: Full TypeScript support throughout
- [x] Security: Multi-tenant isolation and authentication
- [x] Performance: Optimized queries and efficient image handling

## 🚀 Ready for Production
The inquiry comment feature with image support is now fully functional and ready for testing and production deployment!

# 🎉 IMPLEMENTATION COMPLETE

All tasks have been successfully implemented! The inquiry comment feature is now fully functional with:

## [x] Backend API (TRPC)
- `trpc.inquiries.comments.list` - Lists all comments for an inquiry with user data
- `trpc.inquiries.comments.create` - Creates new comments with proper validation

## [x] Frontend Components
- `InquiryCommentList` - Displays comments with avatars, timestamps, and proper styling
- `InquiryCommentForm` - Form for posting new comments with validation and error handling
- Integrated into `InquiryDetails` page below the inquiry form

## 🚀 Features Implemented
- [x] View all comments for an inquiry (chronological order)
- [x] Post new comments with form validation (1-1000 characters)
- [x] Real-time comment list updates after posting
- [x] Proper loading states and error handling
- [x] User avatars and formatted timestamps
- [x] Responsive Material UI design
- [x] Multi-tenant support with proper data isolation
- [x] Authentication integration (comments tied to logged-in user)

## 🧪 Ready for Testing
The feature is ready for testing! Navigate to any inquiry detail page to see the comment section below the inquiry form.

# 🔄 ENHANCEMENT UPDATE - Data Pre-fetching

## Enhancement: Centralized Data Loading
- [x] Updated `_InquiryDetailsPage.tsx`
  [x] Pre-fetch comments data at page level using `trpc.inquiries.comments.list.useQuery({ inquiryId: id })`
  [x] Single loading spinner for both inquiry and comments data
  [x] Pass comments data as props to child components

- [x] Updated `InquiryDetails.tsx`
  [x] Accept `comments: InquiryComments` as prop
  [x] Pass comments data to `InquiryCommentList` component
  [x] Added proper TypeScript types from `lambda-api`

- [x] Refactored `InquiryCommentList.tsx`
  [x] Removed internal TRPC query - now accepts comments as props
  [x] Removed loading and error states (handled at page level)
  [x] Fixed deprecated MUI CardHeader props (titleTypographyProps, subheaderTypographyProps)
  [x] Cleaner component focused only on rendering

## Benefits of Enhancement
- [x] Single Loading State: Only one spinner for the entire page
- [x] Better Performance: Parallel data fetching at page level
- [x] Cleaner Architecture: Separation of data fetching and presentation
- [x] Reduced Boilerplate: No duplicate loading/error handling code
- [x] Better UX: Faster perceived loading time

## 🎨 UI Consistency Update - ImageAvatar Component

- [x] Updated `InquiryCommentList.tsx`
  [x] Replaced basic MUI `Avatar` with predefined `ImageAvatar` component
  [x] Consistent styling with other components in the project
  [x] Automatic fallback to user initials when no image is provided
  [x] Uses project's primary color theme for avatar background
  [x] Maintains same functionality with better consistency
