# Prompt for AI agent
📘 Project context:
  This project called "maphin" handles the task creation flow in a multi-tenant SaaS platform.
  It includes a form using React Hook Form + Zod for validation, and uses React Query for submitting to a backend API.

  📦 Stack:
  React + TypeScript + React Hook Form + Zod + React Query + Material UI + Prisma(Nodejs + Postgres sql), Material UI

  📦 Local execute:
  - MacBook 2020 Intel core i5 chip.
  - Volta for npm version manager
  - profile : maphin
  - environment: xcong ( other env is fuka, stage, prod, just use xcong is enough)

Note:
1. understand and clearly about my project and coding rule and proposal best practice code match with project context even i was specify step to you.
2. understand about task which assignment for you.


# Task context:
### Implement Inquiry Assignment with History and Conditional Notification

I want to enhance the inquiry management system to support **assignment of users** with **assignment history versioning** and **notifications**, with the following **revised requirements**.

Please update the implementation accordingly and update `augment-code/INQUIRY-ASSIGNMENT.md`.

---

## ✅ Feature Summary

Add the ability to assign/unassign users to inquiries, record each action in version history, and notify only the assigned users (except self-assign).

---

## 📌 Revised Requirements

### 1. Assignment Modal

**Frontend:**

* Create `AssignUserModal.tsx` (`apps/web/src/features/inquiries/details/AssignUserModal.tsx`)

  * Dropdown to select users using `trpc.users.list`
  * Button to trigger assignment via `trpc.inquiries.assign.useMutation()`
  * Display modal next to the **Edit button** on the Inquiry Details page (`apps/web/src/features/inquiries/details/InquiryDetails.tsx`)
  * Modal can:

    * Assign a user
    * Unassign a user (set to null)
    * Change assignment

---

### 2. Version History for Assignment

**Backend:**

* Every time an assignment is made/changed/removed:

  * Create a **new version** of the `Inquiry` via the `versions` self-relation
  * Store assignment state in the version

**Example Integration:**

In `create.ts` and `update.ts`:

```ts
await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });
```

* Extend this to also include `assignUsers` in the version if relevant.
* Create new version for:

  * Assign
  * Unassign
  * Reassign

> 🔍 You can model after `tx.inquiry.create()` in `createInquiryMutation`.

---

### 3. Notification Behavior

**Notification rules:**

* Only notify the **assigned user**.
* **Do not send notification** if the assigner is assigning themselves.
* Reference file: `lambdas/api/src/procedures/bicycles/notification/update.ts`

**Code pattern:**

```ts
if (assigneeId && assigneeId !== ctx.session.user.id) {
  // Create notification and link it to the assignee
}
```

* Add similar logic in both inquiry **create** and **assign/unassign** procedures.

---

### 🧪 Edge Cases

* Assign to the same user: no-op or skip versioning.
* Assign to another user: version + notification (if not self).
* Unassign user: version + no notification.

---

### 📂 Files to Touch

#### Backend:

* `lambdas/api/src/procedures/inquiries/assign.ts`
* `lambdas/api/src/procedures/inquiries/unassign.ts`
* `lambdas/api/src/procedures/inquiries/create.ts` (already partially updated)
* Add versioning logic where needed

#### Frontend:

* `AssignUserModal.tsx`
* `InquiryDetails.tsx` (show assign status, trigger modal near Edit)
* `NotificationList.tsx` (already works, just ensure the new notification appears correctly)

---

### 🔁 Summary of Behaviors

| Action        | Version Created | Notification Sent?   |
| ------------- | --------------- | -------------------- |
| Assign user   | ✅               | ✅ (only if not self) |
| Reassign user | ✅               | ✅ (only if not self) |
| Unassign user | ✅               | ❌                    |
| Self assign   | ✅               | ❌                    |
