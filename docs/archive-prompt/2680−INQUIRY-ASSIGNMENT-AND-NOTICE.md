# Prompt for AI agent
📘 Project context:
  This project called "maphin" is a multi-tenant SaaS platform for bicycle management with inquiry handling functionality.
  It uses React Hook Form + Zod for validation, React Query (TanStack Query) for data fetching, and tRPC for type-safe API communication.

  📦 Stack:
  - Frontend: React + TypeScript + React Hook Form + Zod + React Query + Material UI
  - Backend: Node.js + tRPC + Prisma + PostgreSQL
  - Infrastructure: AWS Lambda + CDK

  📦 Local execute:
  - MacBook 2020 Intel core i5 chip
  - Volta for npm version manager
  - AWS profile: maphin
  - Environment: xcong (other envs: fuka, stage, prod)

📋 Coding Rules & Conventions:
1. **No inline comments** - Code should be self-documenting
2. **Explicit null/undefined checks** - Use `isNullish()` from common package
3. **Proper async handling** - Always handle promises correctly
4. **Use predefined values** from `@/contexts/app-context.ts` and `models/label.ts`
5. **Use ImageAvatar component** at `@/components/ImageAvatar.tsx` instead of MUI Avatar
6. **Pre-fetch data at parent level** - Pass data to children rather than fetching in individual components
7. **Strict TypeScript** - Use strict mode with proper type definitions
8. **Package managers only** - Never manually edit package.json, use npm/yarn commands
9. **Biome formatting** - 100 char line width, single quotes, 2-space indentation

📋 Project Structure:
- `apps/web/` - React frontend application
- `lambdas/api/` - tRPC API backend
- `packages/database/` - Prisma schema and migrations
- `packages/common/` - Shared types and utilities
- `packages/models/` - Business logic and label definitions

Note:
1. Understand the project architecture and follow established patterns
2. Implement features that integrate seamlessly with existing code
3. Use the existing notification system and versioning patterns


# Task context:
### Implement Inquiry Assignment with History and Conditional Notification

I want to enhance the inquiry management system to support **assignment of users** with **assignment history versioning** and **notifications**, with the following **revised requirements**.

Please update the implementation accordingly and update `augment-code/INQUIRY-ASSIGNMENT.md`.

---

## ✅ Feature Summary

Add the ability to assign/unassign users to inquiries, record each action in version history, and notify only the assigned users (except self-assign).

---

## 📌 Implementation Requirements
### 1. Assignment Modal Component
**Frontend Implementation:**
Create `AssignUserModal.tsx` at `apps/web/src/features/inquiries/details/AssignUserModal.tsx`:

* **User Selection Dropdown:**
  - Use `trpc.users.list.useQuery()` to fetch available users
  - Display users with `displayName` or fallback to `name`
  - Include "Unassign" option to remove assignment

* **Assignment Actions:**
  - Use `trpc.inquiries.assign.useMutation()` for assignment operations
  - Handle assign, unassign, and reassign scenarios
  - Show loading states during mutations

* **Modal Integration:**
  - Position next to Edit button in `InquiryDetails.tsx`
  - Use Material UI Dialog component
  - Follow existing modal patterns in the codebase

* **Current Assignment Display:**
  - Show currently assigned user(s) in the inquiry details
  - Display assignment history in the events timeline

---

### 2. Backend Assignment Procedures

**Create Assignment API Endpoints:**

`lambdas/api/src/procedures/inquiries/assign.ts`:
```ts
// Input schema for assignment
const AssignInputSchema = z.object({
  inquiryId: z.string().uuid(),
  userId: z.string().uuid().nullable(), // null for unassign
}).strict();

// Assignment mutation with versioning
export const assignInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { inquiryId, userId } = input;
  const { asyncTx, userId: assignerId } = ctx;

  await asyncTx(async (tx) => {
    // Get current inquiry
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id: inquiryId },
      include: { assignUsers: true }
    });

    // Create version with assignment change
    const versionData = {
      ...currentInquiry,
      assignUsers: userId ? { create: { userId } } : { deleteMany: {} }
    };

    await tx.inquiry.create({
      data: { ...versionData, original: { connect: { id: inquiryId } } }
    });

    // Create notification if assigning to different user
    if (userId && userId !== assignerId) {
      await createInquiryAssignmentNotification(tx, {
        inquiryId,
        assigneeId: userId,
        assignerId,
        title: `問い合わせが割り当てられました`,
        description: `問い合わせ「${currentInquiry.title}」があなたに割り当てられました。`
      }, ctx);
    }
  });
};
```

---

### 3. Notification System Integration

**Notification Creation Pattern:**

Based on existing notification system (`packages/database/prisma/schema/user.prisma`):

```ts
// Utility function for inquiry assignment notifications
const createInquiryAssignmentNotification = async (
  tx: Tx,
  data: {
    inquiryId: string;
    assigneeId: string;
    assignerId: string;
    title: string;
    description: string;
  },
  ctx: Context
) => {
  // Create notification record
  const notification = await tx.notification.create({
    data: {
      title: data.title,
      description: data.description,
      type: 'user',
      scope: 'all', // Will be filtered by NotificationsOnUsers
    }
  });

  // Link notification to specific user
  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.assigneeId,
    }
  });
};
```

**Notification Rules:**
- Only notify the assigned user
- Skip notification if self-assignment (`assigneeId === assignerId`)
- Use existing notification display system (`NotificationList.tsx`)

---

### 4. Database Schema Integration

**Existing Schema Usage:**

The inquiry assignment uses existing relations:
- `Inquiry.assignUsers` → `InquiriesOnUsers[]` (many-to-many)
- `User.assignInquiries` → `InquiriesOnUsers[]`
- Versioning via `Inquiry.versions` self-relation

**Assignment State in Versions:**
- Each assignment change creates new inquiry version
- Version includes current `assignUsers` state
- Maintains audit trail of all assignment changes

---

### 5. Frontend Integration Points

**InquiryDetails.tsx Updates:**
- Add assignment display section
- Show currently assigned users
- Add "Assign" button next to Edit button
- Display assignment in events timeline

**Assignment Display:**
```tsx
// Show current assignment
const currentAssignment = inquiry.assignUsers?.[0]?.user;

<Stack direction="row" spacing={1} alignItems="center">
  <Typography variant="body2">担当者:</Typography>
  {currentAssignment ? (
    <Chip
      label={currentAssignment.displayName || currentAssignment.name}
      size="small"
    />
  ) : (
    <Typography variant="body2" color="text.secondary">
      未割り当て
    </Typography>
  )}
  <Button onClick={handleAssignClick}>割り当て</Button>
</Stack>
```

---

### 🧪 Edge Cases & Error Handling
**Assignment Logic:**
- Same user assignment: Skip versioning, show message
- User already assigned: Allow reassignment with version
- Unassign operation: Create version, and create notification for notice to user.
- Invalid user ID: Validate against `trpc.users.list`

**Error Scenarios:**
- Inquiry not found: Use `findUniqueOrThrow`
- User not found: Validate user exists in tenant
- Permission checks: Ensure user can assign inquiries
- Concurrent assignments: Handle with database constraints

---

### 📂 Implementation Files

**Backend Files:**
- `lambdas/api/src/procedures/inquiries/assign.ts` (new)
- `lambdas/api/src/procedures/inquiries/create.ts` (update for initial assignment)
- `lambdas/api/src/procedures/inquiries/get.ts` (include assignUsers)
- Add to tRPC router configuration

**Frontend Files:**
- `apps/web/src/features/inquiries/details/AssignUserModal.tsx` (new)
- `apps/web/src/features/inquiries/details/InquiryDetails.tsx` (update)
- Update inquiry types to include assignment data

**Shared Types:**
- Extend inquiry types in `lambda-api` package
- Add assignment-related Zod schemas in `common` package

---

### 🔁 Summary of Behaviors

| Action        | Version Created | Notification Sent?   |
| ------------- | --------------- | -------------------- |
| Assign user   | ✅               | ✅ (only if not self) |
| Reassign user | ✅               | ✅ (only if not self) |
| Unassign user | ✅               | ✅ (only if not self) |
| Self assign   | ✅               | ❌                    |

## 